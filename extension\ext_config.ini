[INCLUDE: common/materials_interior.ini]
[INCLUDE: common/materials_glass.ini]
[INCLUDE: common/custom_emissive.ini]
[INCLUDE: common/selflighting.ini]
[INCLUDE: common/materials_carpaint.ini]

[MESH_SPLIT_...]
MESHES = plastic lining of the wheel arch
MODE = COPY_FLIPPED

[MESH_SPLIT_...]
MESHES = side fender,doors_SUB0
MODE = COPY_FLIPPED
SPLIT_MATERIAL = EXT_Carpaint

[SHADER_REPLACEMENT_...]
MATERIALS = EXT_Carpaint
CAST_SHADOWS = 1
DOUBLE_FACE_SHADOW_BIASED = 1

[EXTRA_FX]
MASK_GBUFFER = 81_T,226_T,74_T,1341235,far
SKIP_GBUFFER = 


[Material_Glass]
Materials=glass
MaskPass=1
EXTRA_MASK_PASS_COLOR=0,200,150
ThicknessMult=5
Desaturate=0
FilmIOR=1.8

[Material_Glass]
Materials=light_glass,light_int_glass
MaskPass=1
EXTRA_MASK_PASS_COLOR=0,0,0
ThicknessMult=1
Desaturate=0
FilmIOR=1.8



[Material_Glass]
Materials=light_int_glass
MaskPass=1
EXTRA_MASK_PASS_COLOR=0,0,0
ThicknessMult=1
Desaturate=0
FilmIOR=2W

===========int=============



[Material_Aluminium_v2]
Materials =xuanniu_nm,bose_logo
BrightnessAdjustment = 0.15
DetailScale = 0
Metalness = 0.2
Reflectance = 0
LocalReflectionsSharpness = 0.4

[Material_CarPaint_Chrome]
Materials =int_metal
BrightnessAdjustment = 0.8
UseMetallicReflections = 0.9
ClearCoatThickness = 0.5
NormalsMode = BASIC

[Material_Velvet_v2]
Materials = int_alcantara
BrightnessAdjustment = 0.1
Reflectance = 0.3
DetailNormalBlend = 1
LocalReflectionsForce = 1
DetailScale = 30

[Material_Velvet_v2]
Materials = seat_alcantara
BrightnessAdjustment = 0.2
Reflectance = 0.1
DetailNormalBlend = 0.5
LocalReflectionsForce = 1
DetailScale = 8

[Material_Velvet_v2]
Materials = ditan_1,ditan_2
BrightnessAdjustment = 0.1
Reflectance = 0.1
DetailNormalBlend = 0.5
LocalReflectionsForce = 1
DetailScale = 8



[Material_InteriorPBRDetail]
Materials = int_metal,small_nm_metal,grid_metal,body_metal,caliper_metal
Reflectance = 0.2
Smoothness = 0.9
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.4
CubemapReflectionBlur = 0.2
Metalness = 0.9

[Material_InteriorPBRDetail]
Materials = paiqi_metal
Reflectance = 0.2
Smoothness = 0.7
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 1
CubemapReflectionBlur = 0.2
Metalness = 0.9

[Material_InteriorPBRDetail]
Materials = wheel_metal,Material #2
Reflectance = 0.5
Smoothness = 0.9
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.3
CubemapReflectionBlur = 0.2
Metalness = 1

[Material_InteriorPBRDetail]
Materials = logo_metal
Reflectance = 0.2
Smoothness = 0.9
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.1
CubemapReflectionBlur = 0.2
Metalness = 0.8

[Material_InteriorPBRDetail]
Materials = logo_white
Reflectance = 0.4
Smoothness = 0.4
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 1
CubemapReflectionBlur = 0.2
Metalness = 0.6


[Material_InteriorPBRDetail]
Materials = int_dark_metal,small_nm_dark_metal
Reflectance = 0.5
Smoothness = 0.4
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.2
CubemapReflectionBlur = 0.2
Metalness = 0.9

[Material_InteriorPBRDetail]
Materials = m_shift_metal
Reflectance = 0.3
Smoothness = 0.2
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.1
CubemapReflectionBlur = 0.2
Metalness = 1



[Material_InteriorPBRDetail]
Materials = nm_bopian
Reflectance = 0.1
Smoothness = 0.9
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.06
CubemapReflectionBlur = 0.2
Metalness = 0.9

[Material_InteriorPBRDetail]
Materials = int_plastic,arrow_base,point
Reflectance = 0.1
Smoothness = 0.2
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 1.5
CubemapReflectionBlur = 0.2
Metalness = 0.85



[Material_InteriorPBRDetail]
Materials = int_bright_black,shift_display
Reflectance = 0.1
Smoothness = 0.9
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0
CubemapReflectionBlur = 0.2
Metalness = 0



[Material_InteriorPBRDetail]
Materials = stereo
Reflectance = 0.1
Smoothness = 0.2
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.02
CubemapReflectionBlur = 0.2
Metalness = 0.1

[Material_InteriorPBRDetail]
Materials = stereO2
Reflectance = 0.1
Smoothness = 0.2
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.02
CubemapReflectionBlur = 0.2
Metalness = 0.1


[Material_InteriorPBRDetail]
Materials = charger_nm
Reflectance = 0.01
Smoothness = 0.2
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.0
CubemapReflectionBlur = 0.2
Metalness = 0.1

[Material_InteriorPBRDetail]
Materials = int_red
Reflectance = 0.0
Smoothness = 0.3
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 1
BrightnessAdjustment = 0.4
CubemapReflectionBlur = 0.2
Metalness = 0.1


[Material_InteriorPBRDetail]
Materials = bool_sp_ao_palstic
Reflectance = 0.05
Smoothness = 0.1
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0
CubemapReflectionBlur = 0.2
Metalness = 0

[Material_InteriorPBRDetail]
Materials = body_black_mosha,wiper
Reflectance = 0.1
Smoothness = 0.3
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.15
CubemapReflectionBlur = 0.2
Metalness = 0.1





[Material_InteriorPBRDetail]
Materials = lightao_chrome
Reflectance = 0.5
Smoothness = 0.5
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 1
BrightnessAdjustment = 0.5
CubemapReflectionBlur = 0.2
Metalness = 0.8



[Material_InteriorPBRDetail]
Materials = light_black_nm
Reflectance = 0.3
Smoothness = 0.9
DetailNormalPBRFormat = 0
DetailNormalBlend = 0
DetailScale = 1
BrightnessAdjustment = 0
CubemapReflectionBlur = 0.2
Metalness = 0.3



[Material_Plastic_v2] 
Materials = caliper_color
Reflectance = 0.1
Smoothness = 0.2
DetailNormalBlend = 0.5
DetailScale = 10
BrightnessAdjustment = 1
DetailNormalPBRSmoothness = 0.5

[Material_Plastic_v2]
Materials = rear_light_black
Reflectance = 0.1
Smoothness = 0.6
DetailNormalBlend = 0
DetailScale = 0
BrightnessAdjustment = 0.1
DetailNormalPBRSmoothness = 0.9

[Material_Plastic_v2]
Materials = body_black
Reflectance = 0.08
Smoothness = 1
DetailNormalBlend = 0
DetailScale = 64
BrightnessAdjustment = 0.05

[Material_Plastic_v2]
Materials = Material #2125753002, Material #2125753007
Reflectance = 0.08
Smoothness = 0.6
DetailNormalBlend = 0
DetailScale = 1
BrightnessAdjustment = 0.1

[Material_InteriorPBRDetail]
Materials = int_leather,int_leather_small,bool_sp_ao_leather
Reflectance = 0.05
Smoothness = 0.3
DetailNormalBlend = 1
DetailScale = 40
BrightnessAdjustment = 0.5
CubemapReflectionBlur = 0.2
DetailNormalPBRSmoothness = 0.3
ApplyTilingFix = 1

[Material_InteriorPBRDetail]
Materials = m_shift_leather
Reflectance = 0.03
Smoothness = 0.1
DetailNormalBlend = 1
DetailScale = 10
BrightnessAdjustment = 0.5
CubemapReflectionBlur = 0.2
DetailNormalPBRSmoothness = 0.3
ApplyTilingFix = 1

[Material_InteriorPBRDetail]
Materials = airbag_nm
Reflectance = 0.05
Smoothness = 0.3
DetailNormalBlend = 1
DetailScale = 0.46
BrightnessAdjustment = 0.5
CubemapReflectionBlur = 0.2
DetailNormalPBRSmoothness = 0.3
ApplyTilingFix = 1


[Material_InteriorPBRDetail]
Materials = seat_leather
Reflectance = 0.05
Smoothness = 0.1
DetailNormalBlend = 1
DetailScale = 30
BrightnessAdjustment = 0.3
CubemapReflectionBlur = 0.2
DetailNormalPBRSmoothness = 0
ApplyTilingFix = 1




[Material_Leather]
Materials= seat_circle
Reflectance = 0.05
Brightness=0.15
Smoothness = 0.1




[Material_InteriorPBRDetail]
Materials = steer_leather_2
Reflectance = 0.2
Smoothness = 0.1
DetailNormalBlend = 1
DetailScale = 500
BrightnessAdjustment = 1
CubemapReflectionBlur = 0.2
DetailNormalPBRSmoothness = 0
ApplyTilingFix = 1

[Material_InteriorPBRDetail]
Materials = steer_leather
Reflectance = 0.2
Smoothness = 0.1
DetailNormalBlend = 1
DetailScale = 40
BrightnessAdjustment =0.4
CubemapReflectionBlur = 0.2
DetailNormalPBRSmoothness = 0
ApplyTilingFix = 1

[Material_InteriorPBRDetail]
Materials = nm_steer
Reflectance = 0.2
Smoothness = 0.1
DetailNormalBlend = 1
DetailScale = 3
BrightnessAdjustment = 1
CubemapReflectionBlur = 0.2
DetailNormalPBRSmoothness = 0
ApplyTilingFix = 1

[Material_InteriorPBRDetail]
Materials = wheel_color
Reflectance = 0.15
Smoothness = 0.9
DetailNormalBlend = 1
DetailScale = 8
BrightnessAdjustment = 0.8
CubemapReflectionBlur = 0.2
DetailNormalPBRSmoothness = 0





=================灯==============

[LIGHT_HEADLIGHT_0]
COLOR=1,1,1.1,30 			; 颜色  红，绿，蓝，亮度
POSITION=0, 0.6, 2	；位置 （如果灯是对称的，把X轴坐标写在镜像轴里，这里X写0）左右/高度/前后
MIRROR=0.76			；镜像轴（灯和车对称轴的距离）
LOWBEAM_RANGE_MAX=80 	;近照灯最远距离
LOWBEAM_RANGE_MULT = 0.9	；近照灯亮度倍数（没啥用，可以调颜色亮度）
HIGHBEAM_RANGE_MAX=150	；远光灯最远距离
DIRECTION=0, 0,1	;方向 X左右，Y俯仰，Z旋转(这行可以直接删掉，默认数据也很好用)


[EMISSIVE_LIGHT_0]
NAME=reflector
COLOR=150,150,150,5
LAG=0.3		
BIND_TO_HEADLIGHTS=1

[EMISSIVE_HIGHBEAM_0]
NAME=far
COLOR= 1,1.4,1.5,150	 ; 颜色  红，绿，蓝，亮度
LAG = 0.1 			；熄灭延迟，最大0.9
SIMULATE_HEATING = 0		;灯在熄灭时会变黄，模拟老式灯泡

[EMISSIVE_HIGHBEAM_1]
NAME=headlight
COLOR= 1,1,1,3	 ; 颜色  红，绿，蓝，亮度
LAG = 0.1 			；熄灭延迟，最大0.9
SIMULATE_HEATING = 0		;灯在熄灭时会变黄，模拟老式灯泡
TOGGLE_VISIBILITY = 1


[EMISSIVE_LIGHT_1]
NAME= 313,339
COLOR=1,1,1,5
OFF_COLOR = 1,1,1,3
LAG=0.3		
BIND_TO_HEADLIGHTS=1

[EMISSIVE_LIGHT_...]
NAME= 315
COLOR=1,1,1,3

LAG=0.3		
BIND_TO_HEADLIGHTS=1

[EMISSIVE_LIGHT_2]
NAME= 301,304,307
COLOR=1,1,1,8
OFF_COLOR = 1,1,1,3
LAG=0.3		
BIND_TO_HEADLIGHTS=1


[EMISSIVE_LIGHT_3]
NAME=121351
COLOR=1,1.4,1.2,90
LAG=0.3			

BIND_TO_HEADLIGHTS=1  

[EMISSIVE_LIGHT_4]
NAME=76_T
COLOR=20,15,1,200
LAG=0.3			

BIND_TO_HEADLIGHTS=1   

[EMISSIVE_LIGHT_5]
NAME=LED
COLOR=1,1,1,200
OFF_COLOR = 1,1,1,100
LAG=0.3			

[EMISSIVE_LIGHT_6]
NAME=232
COLOR=1,1.4,1.4,1000
OFF_COLOR = 1,1.4,1.4,500
LAG=0.3			
BIND_TO_HEADLIGHTS=1  


[EMISSIVE_LIGHT_7]
NAME=position
COLOR=1,1,1,2
LAG=0.3			
BIND_TO_HEADLIGHTS=1 
TOGGLE_VISIBILITY = 1

[EMISSIVE_LIGHT_8]
NAME=139_T
COLOR=30,1,1,10
LAG=0.3			
BIND_TO_HEADLIGHTS=1 

[EMISSIVE_LIGHT_9]
NAME=243
COLOR=30,10,1,50
LAG=0.3			
BIND_TO_HEADLIGHTS=1 

[EMISSIVE_LIGHT_10]
NAME=238
COLOR=10,15,15,10
LAG=0.3			
BIND_TO_HEADLIGHTS=1 

[EMISSIVE_LIGHT_11]
NAME=71_T
COLOR=2,1,1,5
LAG=0.3			
BIND_TO_HEADLIGHTS=1 

[EMISSIVE_LIGHT_12]
NAME=rear_light_new
COLOR=30,1.5,1,0.03
LAG=0.3			
BIND_TO_HEADLIGHTS=1 


[EMISSIVE_...]
NAME=up
INPUT = RPM
COLOR=1,1,1,10
LAG=0
INPUT_THRESHOLD = 7000	
TOGGLE_VISIBILITY = 1	


[EMISSIVE_TURNSIGNAL_LEFT_...]
NAME=left2	
COLOR=20,10,1,10
LAG=0.0 
CAST_LIGHT=1
LOCATION = FRONT		

[EMISSIVE_TURNSIGNAL_RIGHT_...]
NAME=right2
COLOR=20,10,1,10
LAG=0.0 
CAST_LIGHT=1
LOCATION = FRONT


[EMISSIVE_TURNSIGNAL_LEFT_...]
NAME=turn_left	
COLOR=1,1,1,5
LAG=0.0 
CAST_LIGHT=0	
TOGGLE_VISIBILITY = 1	

[EMISSIVE_TURNSIGNAL_RIGHT_...]
NAME=turn_right
COLOR=1,1,1,5
LAG=0.0 
CAST_LIGHT=0	
TOGGLE_VISIBILITY = 1	

[EMISSIVE_TURNSIGNAL_LEFT_...]
NAME=turn_m_l
COLOR=50,10,1,20
LAG=0.0 
CAST_LIGHT=1	
LOCATION = FRONT

[EMISSIVE_TURNSIGNAL_RIGHT_...]
NAME=turn_m_r
COLOR=50,10,1,20
LAG=0.0 
CAST_LIGHT=1	
LOCATION = FRONT


[EMISSIVE_TURNSIGNAL_LEFT_...]
NAME=rear_left
COLOR=18,10,1,0.3
LAG=0.0 
CAST_LIGHT=1
LOCATION=REAR		

[EMISSIVE_TURNSIGNAL_RIGHT_...]
NAME=rear_right
COLOR=15,10,1,20
LAG=0.0 
CAST_LIGHT=1
LOCATION=REAR	

[EMISSIVE_TURNSIGNAL_LEFT_...]
NAME=252_T
COLOR=200,80,1,0.01
OFF_COLOR = 1,1,1,5
LAG=0.0 
CAST_LIGHT=1
CHANNEL = 3
LOCATION=FRONT		

[EMISSIVE_TURNSIGNAL_RIGHT_...]
NAME=252_T
COLOR=200,80,1,0.01
OFF_COLOR = 1,1,1,5
LAG=0.0 
CHANNEL = 4
CAST_LIGHT=1
LOCATION=FRONT


[EMISSIVE_TURNSIGNAL_LEFT_...]
NAME=f_l_light
COLOR=200,80,1,10
OFF_COLOR = 1,1,1,5000
LAG=0.0 
CAST_LIGHT=1
LOCATION=FRONT		

[EMISSIVE_TURNSIGNAL_RIGHT_...]
NAME=f_r_light
COLOR=200,80,1,10
OFF_COLOR = 1,1,1,5000
LAG=0.0 
CAST_LIGHT=1
LOCATION=FRONT

[EXTRA_FX_EMISSIVE_...]
MESHES =  rear_left,rear_right       ; list of meshes
MATERIALS =      ; list of materials
RANGE = 0.013  ; range in meters (by default 0.08 for cars and 5 for tracks)
COLOR =80,10,1,2  ; optional brightness multiplier (could be an RGBA value)
DIRECTED =0             ; set for 0 for point light and 1 for directed light, or 
INTERIOR_ONLY = 0
EXTERIOR_ONLY = 0
EMISSIVE_TWEAK = 1
MIP_BIAS = 5
IGNORE_TEXTURE_COLOR = 0 ; set to 1 to use COLOR and emissive color only
NORMAL_SHIFT = 0


[EXTRA_FX_EMISSIVE_...]
MESHES =  315
MATERIALS =   ; list of materials
RANGE = 0.03; range in meters (by default 0.08 for cars and 5 for tracks)
COLOR =1,1,1,1  ; optional brightness multiplier (could be an RGBA value)
DIRECTED =0            ; set for 0 for point light and 1 for directed light, or 
INTERIOR_ONLY = 0
EXTERIOR_ONLY = 0
EMISSIVE_TWEAK = 1
MIP_BIAS = 5
IGNORE_TEXTURE_COLOR = 1 ; set to 1 to use COLOR and emissive color only
NORMAL_SHIFT = 0

[EXTRA_FX_EMISSIVE_...]
MESHES =  
MATERIALS =arrow_light   ; list of materials
RANGE = 0.02; range in meters (by default 0.08 for cars and 5 for tracks)
COLOR =50,1,1,0.5  ; optional brightness multiplier (could be an RGBA value)
DIRECTED =0             ; set for 0 for point light and 1 for directed light, or 
INTERIOR_ONLY = 0
EXTERIOR_ONLY = 0
EMISSIVE_TWEAK = 1
MIP_BIAS = 5
IGNORE_TEXTURE_COLOR = 1 ; set to 1 to use COLOR and emissive color only
NORMAL_SHIFT = 0

[EXTRA_FX_EMISSIVE_...]
MESHES =  313,339
MATERIALS =   ; list of materials
RANGE = 0.01; range in meters (by default 0.08 for cars and 5 for tracks)
COLOR =1,1,1,0.2  ; optional brightness multiplier (could be an RGBA value)
DIRECTED =0             ; set for 0 for point light and 1 for directed light, or 
INTERIOR_ONLY = 0
EXTERIOR_ONLY = 0
EMISSIVE_TWEAK = 1
MIP_BIAS = 5
IGNORE_TEXTURE_COLOR = 1 ; set to 1 to use COLOR and emissive color only
NORMAL_SHIFT = 0

[EXTRA_FX_EMISSIVE_...]
MESHES =  310
MATERIALS =   ; list of materials
RANGE = 0.08; range in meters (by default 0.08 for cars and 5 for tracks)
COLOR =1,1,1,2  ; optional brightness multiplier (could be an RGBA value)
DIRECTED =1             ; set for 0 for point light and 1 for directed light, or 
INTERIOR_ONLY = 0
EXTERIOR_ONLY = 0
EMISSIVE_TWEAK = 1
MIP_BIAS = 5
IGNORE_TEXTURE_COLOR = 0 ; set to 1 to use COLOR and emissive color only
NORMAL_SHIFT = 0



[EMISSIVE_BRAKE_...]
NAME=Plane004, LEDs upper stop signal
COLOR=40,5,1,20		 ; 颜色  红，绿，蓝，亮度
LAG=0.2				；熄灭延迟，最大0.9

[LIGHT_BRAKES]
COLOR=10, 0, 0, 0.8   ; 颜色  红，绿，蓝，亮度
POSITION=0, 0.4, -2.5 	；位置 （如果灯是对称的，把X轴坐标写在镜像轴里，这里X写0）左右/高度/前后
MIRROR=0.7		；镜像轴（灯和车对称轴的距离）
DIRECTION=-0, -1,-0.1	;




====================================折射======================



[REFRACTING_HEADLIGHT_0]
SURFACE = 72_T
INSIDE =58_T,zhegai
RESOLUTION MULT = 4

ABSORPTION = 0.02
AMBIENT_MULT = 0.25
BASE_EMISSIVE_K = 0
BOUNCED_BACK_MULT = 0.4
BULB_BLUR_K = 9
BULB_REFLECTION_K = 0.2
CUSTOM_BULB_0 = 0.211,0.5,0.01,0.06
CUSTOM_BULB_1 = 0.414,0.5,0.01,0.06
CUSTOM_BULB_2 = 0.628,0.5,0.01,0.06
CUSTOM_BULB_3 = 0.823,0.5,0.01,0.06
DIFFUSE_MAP_FILTER_MULT = 1
DIFFUSE_MAP_MULT = 1
DIRECTION = 0,0,1
DYNAMIC_EMISSIVE_MAP = 0
EMISSIVE_MULT = 100
EXTRA_GLASS_COLORIZATION = 1
EXTRA_SIDE_THICKNESS = 0
F0 = 0.8
GLASS_COLOR = 0.25,0.25,0.25
GLASS_EMISSIVE_MULT = 0
GLASS_EXTRA_THICKNESS = 0.0050000055
INNER_SPECULAR = 10
INNER_SPECULAR_EXP = 800
IOR = 2.3
IOR_FLYOUT_FLAT = 0
LOD_BIAS = -0.5
MIRROR_2_AS_5 = 0
MIRROR_3_AS_4 = 0
MIRROR_DIR = 1,0,0
MIRROR_POS = -1.7881393e-07,0.625388,-1.9494281
NM_SHARE_EXT = 0.49
NM_SHARE_INT = 0.6
ORIGIN = 0.6570357,0.625388,-2.0150445
RADIUS = 0.113256134
REFLECTIVENESS_DIFFUSE_MULT = 60
REFLECTIVENESS_MULT = 10
REFLECTIVE_GAMMA = 1
REFRACTION_MASKING = 0
SIDE_FALLOFF = 0
USE_COLORED_BULBS = 0
USE_CUSTOM_BULBS = 1
USE_NORMAL_ALPHA = 1


[REFRACTING_HEADLIGHT_1]
SURFACE = 76_T
INSIDE =zhegai
RESOLUTION MULT = 2

ABSORPTION = 0.02
AMBIENT_MULT = 0.25
BASE_EMISSIVE_K = 0
BOUNCED_BACK_MULT = 0.4
BULB_BLUR_K = 9
BULB_REFLECTION_K = 0.2
CUSTOM_BULB_0 = 0.559,0.611,0.383,0.016
CUSTOM_BULB_1 = 0.556,0.424,0.383,0.016
CUSTOM_BULB_2 = 0.628,0.5,0,0.06
CUSTOM_BULB_3 = 0.823,0.5,0,0.06
DIFFUSE_MAP_FILTER_MULT = 1
DIFFUSE_MAP_MULT = 0.686
DIRECTION = 0,0,1
DYNAMIC_EMISSIVE_MAP = 0
EMISSIVE_MULT = 44.87
EXTRA_GLASS_COLORIZATION = 1
EXTRA_SIDE_THICKNESS = 0
F0 = 1
GLASS_COLOR = 0.25,0.25,0.25
GLASS_EMISSIVE_MULT = 0
GLASS_EXTRA_THICKNESS = 0.0050000055
INNER_SPECULAR = 10
INNER_SPECULAR_EXP = 800
IOR = 2.5
IOR_FLYOUT_FLAT = 0
LOD_BIAS = 2
MIRROR_2_AS_5 = 0
MIRROR_3_AS_4 = 0
MIRROR_DIR = 1,0,0
MIRROR_POS = -1.7881393e-07,0.625388,-1.9494281
NM_SHARE_EXT = 1
NM_SHARE_INT = 0
ORIGIN = 0.6570357,0.625388,-2.0150445
RADIUS = 0.113256134
REFLECTIVENESS_DIFFUSE_MULT = 60
REFLECTIVENESS_MULT = 10
REFLECTIVE_GAMMA = 1
REFRACTION_MASKING = 0
SIDE_FALLOFF = 0
USE_COLORED_BULBS = 0
USE_CUSTOM_BULBS = 1
USE_NORMAL_ALPHA = 1









[REFRACTING_HEADLIGHT_2]
SURFACE = 139_T
INSIDE =zhegai
RESOLUTION MULT = 4

ABSORPTION = 0.55
AMBIENT_MULT = 0.25
BASE_EMISSIVE_K = 0.04
BOUNCED_BACK_MULT = 0.76
BULB_BLUR_K = 9
BULB_REFLECTION_K = 0.2
CUSTOM_BULB_0 = 0.5,0.5,0,0.045
CUSTOM_BULB_1 = 0.5,0.5,0,0
CUSTOM_BULB_2 = 0.5,0.5,0,0
CUSTOM_BULB_3 = 0.5,0.5,0,0
DIFFUSE_MAP_FILTER_MULT = 1
DIFFUSE_MAP_MULT = 1
DIRECTION = -0.99755174,-0.0046115443,-0.06977999
DYNAMIC_EMISSIVE_MAP = 0
EMISSIVE_MULT = 1.55
EXTRA_GLASS_COLORIZATION = 1
EXTRA_SIDE_THICKNESS = 0
F0 = 1
GLASS_COLOR = 1,0,0
GLASS_EMISSIVE_MULT = 0.02
GLASS_EXTRA_THICKNESS = 0
INNER_SPECULAR = 10
INNER_SPECULAR_EXP = 800
IOR = 1.83
IOR_FLYOUT_FLAT = 0
LOD_BIAS = -1.9
MIRROR_2_AS_5 = 0
MIRROR_3_AS_4 = 0
MIRROR_DIR = 1,0,0
MIRROR_POS = -1.1920929e-07,0.58024883,-1.7285753
NM_SHARE_EXT = 0.02
NM_SHARE_INT = 1
ORIGIN = 0.9452133,0.58065987,-1.7276154
RADIUS = 0.09471846
REFLECTIVENESS_DIFFUSE_MULT = 100
REFLECTIVENESS_MULT = 10
REFLECTIVE_GAMMA = 1
REFRACTION_MASKING = 0
SIDE_FALLOFF = 0.5
USE_COLORED_BULBS = 0
USE_CUSTOM_BULBS = 1
USE_NORMAL_ALPHA = 1

[REFRACTING_HEADLIGHT_3]
SURFACE = 449_T
INSIDE =60
RESOLUTION MULT = 1

ABSORPTION = 0.02
AMBIENT_MULT = 0.25
BASE_EMISSIVE_K = 0.05
BOUNCED_BACK_MULT = 0.4
BULB_BLUR_K = 2
BULB_REFLECTION_K = 0.2
CUSTOM_BULB_0 = 0.229,0.272,0.15,0.231
CUSTOM_BULB_1 = 0.421,0.267,0.15,0.231
CUSTOM_BULB_2 = 0.635,0.267,0.15,0.231
CUSTOM_BULB_3 = 0.852,0.267,0.15,0.231
DIFFUSE_MAP_FILTER_MULT = 1
DIFFUSE_MAP_MULT = 1
DIRECTION = 0,0,1
DYNAMIC_EMISSIVE_MAP = 0
EMISSIVE_MULT = 9.33
EXTRA_GLASS_COLORIZATION = 1
EXTRA_SIDE_THICKNESS = 0
F0 = 0.8
GLASS_COLOR = 1,0.032915592,0
GLASS_EMISSIVE_MULT = 0
GLASS_EXTRA_THICKNESS = 0
INNER_SPECULAR = 10
INNER_SPECULAR_EXP = 800
IOR = 2.03
IOR_FLYOUT_FLAT = 0
LOD_BIAS = -0.5
MIRROR_2_AS_5 = 0
MIRROR_3_AS_4 = 0
MIRROR_DIR = 1,0,0
MIRROR_POS = -8.940697e-08,1.109904,-0.88641536
NM_SHARE_EXT = 0.3
NM_SHARE_INT = 0.42
ORIGIN = 0.14301704,1.109904,-0.8954502
RADIUS = 0.15301713
REFLECTIVENESS_DIFFUSE_MULT = 60
REFLECTIVENESS_MULT = 10
REFLECTIVE_GAMMA = 1
REFRACTION_MASKING = 0
SIDE_FALLOFF = 0
USE_COLORED_BULBS = 0
USE_CUSTOM_BULBS = 1
USE_NORMAL_ALPHA = 1



[REFRACTING_HEADLIGHT_4]
SURFACE = 68_T
INSIDE =zhegai
RESOLUTION MULT = 1

ABSORPTION = 0
AMBIENT_MULT = 0.25
BASE_EMISSIVE_K = 1
BOUNCED_BACK_MULT = 0.4
BULB_BLUR_K = 2
BULB_REFLECTION_K = 0.2
CUSTOM_BULB_0 = 0.5,0.5,0,0
CUSTOM_BULB_1 = 0.5,0.5,0,0
CUSTOM_BULB_2 = 0.5,0.5,0,0
CUSTOM_BULB_3 = 0.5,0.5,0,0
DIFFUSE_MAP_FILTER_MULT = 1
DIFFUSE_MAP_MULT = 0
DIRECTION = 0,0,-1
DYNAMIC_EMISSIVE_MAP = 0
EMISSIVE_MULT = 4.19
EXTRA_GLASS_COLORIZATION = 1
EXTRA_SIDE_THICKNESS = 0
F0 = 0.8
GLASS_COLOR = 1,0.9999,0.9999
GLASS_EMISSIVE_MULT = 0
GLASS_EXTRA_THICKNESS = 0.0050000055
INNER_SPECULAR = 10
INNER_SPECULAR_EXP = 800
IOR = 1.91
IOR_FLYOUT_FLAT = 0
LOD_BIAS = -0.5
MIRROR_2_AS_5 = 0
MIRROR_3_AS_4 = 1
MIRROR_DIR = 1,0,0
MIRROR_POS = 1.7881393e-07,0.51511455,1.8421705
NM_SHARE_EXT = 0.72
NM_SHARE_INT = 0.88
ORIGIN = 0.7031709,0.51511455,1.947644
RADIUS = 0.12265573
REFLECTIVENESS_DIFFUSE_MULT = 60
REFLECTIVENESS_MULT = 10
REFLECTIVE_GAMMA = 1
REFRACTION_MASKING = 0
SIDE_FALLOFF = 0
USE_COLORED_BULBS = 0
USE_CUSTOM_BULBS = 0
USE_NORMAL_ALPHA = 1



[REFRACTING_HEADLIGHT_5]
SURFACE = 252_T
INSIDE =zhegai
RESOLUTION MULT = 1

ABSORPTION = 0
AMBIENT_MULT = 0.25
BASE_EMISSIVE_K = 1
BOUNCED_BACK_MULT = 0.4
BULB_BLUR_K = 2
BULB_REFLECTION_K = 0.2
CUSTOM_BULB_0 = 0.5,0.5,0,0
CUSTOM_BULB_1 = 0.5,0.5,0,0
CUSTOM_BULB_2 = 0.5,0.5,0,0
CUSTOM_BULB_3 = 0.5,0.5,1,0
DIFFUSE_MAP_FILTER_MULT = 1
DIFFUSE_MAP_MULT = 0
DIRECTION = 0,0,-1
DYNAMIC_EMISSIVE_MAP = 0
EMISSIVE_MULT = 4.19
EXTRA_GLASS_COLORIZATION = 1
EXTRA_SIDE_THICKNESS = 0
F0 = 0.8
GLASS_COLOR = 1,0.9999,0.9999
GLASS_EMISSIVE_MULT = 0
GLASS_EXTRA_THICKNESS = 0.0050000055
INNER_SPECULAR = 10
INNER_SPECULAR_EXP = 800
IOR = 1.91
IOR_FLYOUT_FLAT = 0
LOD_BIAS = -0.5
MIRROR_2_AS_5 = 0
MIRROR_3_AS_4 = 1
MIRROR_DIR = 1,0,0
MIRROR_POS = 1.7881393e-07,0.51511455,1.8421705
NM_SHARE_EXT = 0.72
NM_SHARE_INT = 0.88
ORIGIN = 0.726,0.597,1.947644
RADIUS = 0.10290003
REFLECTIVENESS_DIFFUSE_MULT = 60
REFLECTIVENESS_MULT = 10
REFLECTIVE_GAMMA = 1
REFRACTION_MASKING = 0
SIDE_FALLOFF = 0
USE_COLORED_BULBS = 1
USE_CUSTOM_BULBS = 1
USE_NORMAL_ALPHA = 1



[REFRACTING_HEADLIGHT_6]
SURFACE = far
INSIDE =far001
RESOLUTION MULT = 1

ABSORPTION = 0.027999999
AMBIENT_MULT = 0.25
BASE_EMISSIVE_K = 0
BOUNCED_BACK_MULT = 1
BULB_BLUR_K = 2
BULB_REFLECTION_K = 0.2
CUSTOM_BULB_0 = 0.5,0.5,0,0
CUSTOM_BULB_1 = 0.5,0.5,0,0
CUSTOM_BULB_2 = 0.5,0.5,0,0
CUSTOM_BULB_3 = 0.5,0.5,0,0
DIFFUSE_MAP_FILTER_MULT = 0
DIFFUSE_MAP_MULT = 1
DIRECTION = 0,0,-1
DYNAMIC_EMISSIVE_MAP = 0
EMISSIVE_MULT = 10.98
EXTRA_GLASS_COLORIZATION = 0
EXTRA_SIDE_THICKNESS = 96
F0 = 1
GLASS_COLOR = 1,0.9999,0.9999
GLASS_EMISSIVE_MULT = 0.37
GLASS_EXTRA_THICKNESS = 0
INNER_SPECULAR = 19.5
INNER_SPECULAR_EXP = 768
IOR = 2.5
IOR_FLYOUT_FLAT = 1
LOD_BIAS = -2
MIRROR_2_AS_5 = 0
MIRROR_3_AS_4 = 1
MIRROR_DIR = 1,0,0
MIRROR_POS = 2.9802322e-08,0.5647355,1.8071816
NM_SHARE_EXT = 1
NM_SHARE_INT = 1
ORIGIN = 0.6483376,0.5647355,1.8391182
RADIUS = 0.060501434
REFLECTIVENESS_DIFFUSE_MULT = 100
REFLECTIVENESS_MULT = 10
REFLECTIVE_GAMMA = 3
REFRACTION_MASKING = 0
SIDE_FALLOFF = 4
USE_COLORED_BULBS = 0
USE_CUSTOM_BULBS = 0
USE_NORMAL_ALPHA = 0











============仪表=================

[DI_OIL_TEMPERATURE_...]
INPUT_MULT = 1.8
INPUT_ADD = 32
DIGITAL_ITEM = 3
DIGITAL_ITEM_NUMBER_FORMAT = 02.0
UPPER_BOUND = 999999

[DI_OIL_PRESSURE_...]
INPUT_MULT = 14
DIGITAL_ITEM = 4
DIGITAL_ITEM_NUMBER_FORMAT = 02.0
UPPER_BOUND = 999999

[DI_WATER_TEMPERATURE_...]
INPUT_MULT = 1.8
INPUT_ADD = 32
DIGITAL_ITEM = 2
DIGITAL_ITEM_NUMBER_FORMAT = 02.0
UPPER_BOUND = 999999

[DI_WATER_TEMPERATURE_...]
INPUT_MULT = 3
INPUT_ADD = -150
DIGITAL_ITEM = 5
DIGITAL_ITEM_NUMBER_FORMAT = 02.0
UPPER_BOUND = 999999

[DI_DRIVEN_TOTAL_...]
INPUT_MULT = 0.62
DIGITAL_ITEM = 6
DIGITAL_ITEM_NUMBER_FORMAT = 06.0
UPPER_BOUND = 999999



[ANALOG_INDICATOR_...]
INPUT =TURBO_BOOST
OBJECT_NAME=ARROW_BOOST
ZERO=0
STEP=200
MIN_VALUE=0
USE_BAR=0

[ANALOG_INDICATOR_...]
INPUT =TURBO_BOOST
OBJECT_NAME=ARROW_TS
ZERO=0
STEP=450
MIN_VALUE=0
USE_BAR=0

[ANALOG_INDICATOR_...]
INPUT =FUEL              ; any of supported inputs
NAME =ARROW_VOLT        ; name of mesh to move
UPPER_BOUND = 72                ; maximum input value for arrow to move 
LOWER_BOUND = 1                  ; minimum input value
START = 100                     ; offset for starting point, degress
RANGE = 180                     ; full range of arrow to move to at the upper bound, degress 


[ANALOG_INDICATOR_...]
INPUT =OIL_PRESSURE              ; any of supported inputs
NAME =ARROW_OIL_PRESS         ; name of mesh to move
UPPER_BOUND = 10                ; maximum input value for arrow to move 
LOWER_BOUND = 1                  ; minimum input value
START = 0                     ; offset for starting point, degress
RANGE = -120                      ; full range of arrow to move to at the upper bound, degress 

[ANALOG_INDICATOR_...]
INPUT =OIL_TEMPERATURE              ; any of supported inputs
NAME =ARROW_WATER_TEMP         ; name of mesh to move
UPPER_BOUND = 300                ; maximum input value for arrow to move 
LOWER_BOUND = 20                 ; minimum input value
START = 0                     ; offset for starting point, degress
RANGE = -120                      ; full range of arrow to move to at the upper bound, degress 

[SCRIPTABLE_DISPLAY_...]
MESHES =310
SCRIPT = fuel.lua
ACTIVE_FOR_PLAYER_ONLY=0
ACTIVE_FOR_UNFOCUSED=1


[INCLUDE: android_auto/config.ini]
[Display_AndroidAuto]
Meshes = 290_T
Resolution = 1024,1024
Size = 1021, 610        ; 屏幕尺寸
Offset = 1, 207             ; 屏幕区域的左上角坐标
Scale = 1 ; 可能需要调整来保证尺寸正确
RearCameraPosition = 0, 0.25, -4  ; 倒车影像位置


Start = "1, 207", Size = "1021, 610"

[LIGHT_LICENSEPLATE]
MESHES=238
OFFSET=0.03,0.05,0
RANGE=0.24
RANGE_GRADIENT_OFFSET=1
SPOT = 120
SPOT_SHARPNESS=0.3
COLOR=1,1,1,15
FALLBACK_EMISSIVE=1,0.7,0.3,0.9
FADE_AT=100
FALLBACK_LOWBEAM_COLOR=1,0.7,0.3,0.9


=============selflight


[SelfLight]
POSITION=0, 0.558, 1.695	；位置 （如果灯是对称的，把X轴坐标写在镜像轴里，这里X写0）左右/高度/前后
MIRROR=0.760				；镜像轴（灯和车对称轴的距离）
SPOT=100				；可以理解为亮度，或者光密度
RANGE=0.2				；范围
COLOR = 1,1.1,1.2,100		； 颜色  红，绿，蓝，亮度
BIND_TO_HEADLIGHTS=1	；在车头灯亮的时候跟着一起亮，和上一行二选一
DIRECTION = 0,0.2,1		  ;方向 X左右，Y俯仰，Z旋转   （自己多找找感觉）

[EXHAUST_FLAMES]
ANTILAG=0						; turbo antilag
ANTILAG_DELAY=0.0					; how long after antilag trigger do flames start, in seconds
ANTILAG_INTENSITY=0 ; flame size
ANTILAG_INTERVAL_MAX=0
ANTILAG_INTERVAL_MIN=0
ANTILAG_TURBO_THRESHOLD=0
DAMAGE=0						; engine damage?
DAMAGE_DURATION=0.00
DAMAGE_INTENSITY=0
DAMAGE_PROBABILITY=0
DISCHARGE=1						; lifting off the throttle
DISCHARGE_CHARGE_TIME=0				; how long you have to press the throttle to "charge" the flames
DISCHARGE_DELAY=0.0					; delay after throttle lift
DISCHARGE_DURATION=0				; length of flame shooting, in seconds
DISCHARGE_PROBABILITY=0				; chance of flame shoot on lift, 1 is guaranteed, 0 is never
DISCHARGE_INTENSITY=0.0			; flame size
DISCHARGE_RPM_THRESHOLD_MAX=00			; self explanatory
DISCHARGE_RPM_THRESHOLD_MIN=000			; self explanatory
DISCHARGE_WATER_TEMPERATURE_THRESHOLD=0
FLAT_SHIFT=0						; on upshift
FLAT_SHIFT_COOLDOWN=0.0				; after upshift
FLAT_SHIFT_DURATION=0				; length of flame shooting, in seconds
FLAT_SHIFT_GAS_THRESHOLD=0				; how much gas needs to be pressed on upshift for flame, 100 is wide open
FLAT_SHIFT_INTENSITY=0				; flame size
FLAT_SHIFT_PROBABILITY=0				; chance of flame shoot on upshift
FLAT_SHIFT_RPM_THRESHOLD=0
LIMITER=0						; on hitting the limiter
LIMITER_DELAY=0					; delay, in seconds
LIMITER_INTENSITY=0					; flame size
LIMITER_RPM_THRESHOLD=0				; limiter flames will not shoot below this rpm


