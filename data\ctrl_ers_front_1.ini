[HEADER]
NAME=Race Hybrid

[CONTROLLER_0]
COMBINATOR=ADD 							; COMBINATOR MODE: ADD or MULT
INPUT=GAS								; telemetry channel input. SLIPRATIO_MAX SLIPRATIO_AVG LONG LATG BRAKE0-1 GAS0-1 STEER-1+1 SPEEDKMH GEAR
LUT=kers_gas_fwd.lut						; input data to kers delivery lookup table
FILTER=0.96								; filter for eliminating spikes in variations and control variation speed
UP_LIMIT=1.0								; kers delivery max limit
DOWN_LIMIT=0							; kers delivery min limit (can be negative)

[CONTROLLER_1]
INPUT=OVERSTEER_FACTOR
COMBINATOR=MULT
LUT=(-1.0=0.15|0=1|1=1.0)
FILTER=0.96
UP_LIMIT=1.0
DOWN_LIMIT=0.0



[CONTROLLER_2]
INPUT=RPMS
COMBINATOR=MULT
LUT=(1000=1|9150=1|9151=0)
FILTER=0.96
UP_LIMIT=1.0
DOWN_LIMIT=0.0

[CONTROLLER_3]
COMBINATOR=MULT 						
INPUT=SPEED_KMH						
LUT=kers_speed.lut			
FILTER=0.96							
UP_LIMIT=1.0								
DOWN_LIMIT=0	
	
	
