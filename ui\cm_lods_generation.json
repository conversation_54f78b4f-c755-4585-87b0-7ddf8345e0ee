{"UserDefinedValues": {"CAR_PAINT": "texture:body.dds", "STICKER_MESHES": null, "INTERIOR_MESHES": null, "REMOVE_COMPLETELY": null, "LESS_IMPORTANT": null, "MORE_IMPORTANT": null}, "Stages": {"CockpitLr": "{\"isActive\":false,\"trianglesCount\":8000,\"applyWeldingFix\":true,\"mergeExceptions\":\"$Lights\\n$Mirrors\\nDAMAGE_GLASS_*\\n*DOOR*\",\"mergeParents\":\"COCKPIT_HR\\nSTEER_HR\",\"mergeAsBlack\":\"\",\"elementsToRemove\":\"$AnalogInstruments\\n$BrokenGlass\\n$ExtraRotating\\n$LightAnimation\\n$RemoveCompletely\\nCOCKPIT_LR\\nCINTURE*\\n*Seatbelt*\\n\",\"elementsPriorities\":\"$MoreImportantMeshes = 1.5\\n$LessImportantMeshes = 0.5\\nSTEER_HR = 1.5\\naabbRelMaxY<0.6 = 0.5\",\"keepTemporaryFiles\":false}", "LodB": "{\"isActive\":true,\"trianglesCount\":40000,\"applyWeldingFix\":true,\"mergeExceptions\":\"$Lights\\n$Mirrors\\n$GlowingBrakeDiscs\\nDAMAGE_GLASS_*\\n*STEER_HR*\\n*WING*\\n*DOOR*\\n*Wiper*\\n*hood*\\n*trunk*\\n*blinker*\\n*light*\\n*brake*\\n*ROOF*\\n*reflector*\\n*indicator*\\n*reverse*\\n*spoiler*\\n*BONNET*\\n*glass*\\n*beam*\\n*bulb*\\n*turn*\\n*lamp*\\n*Blinker*\\n*INNER*\\nPlane004\\nLEDs upper stop signal\\n\",\"mergeParents\":\"$RimsAll\\n$ExtraRotating\\n$LightAnimation\\n$VisualDamage\\n$Wheels\\nREAR_AXLE\\nCOCKPIT_HR\",\"mergeAsBlack\":\"\",\"elementsToRemove\":\"$InteriorMeshes & ( $ExtraRotating, $LightAnimation, $Mirrors, $TransparentMeshes )\\n$RemoveCompletely\\nCOCKPIT_LR\\nCINTURE*\\n*Seatbelt*\",\"emptyNodesToKeep\":\"$ImportantNodes\",\"convertUv2\":\"\",\"elementsPriorities\":\"$InteriorMeshes = 0.6\\n$Lights = 1.8\\n$CarPaint = 2.4\\n$Tyres = 1.6\\n$WheelMeshes = 1.6\\n*light* = 2\\n*brake* = 2\\n*reflector* = 2\\n*indicator* = 2\\n*reverse* = 2\\n*beam* = 2\\n*bulb* = 2\\n*turn* = 2\\n*lamp* = 2\\n*Blinker* = 2\\n*wiper* = 2\\nPlane004 = 2\\nLEDs upper stop signal = 2\",\"offsetsAlongNormal\":\"$Stickers = 0.005\",\"keepTemporaryFiles\":false}", "LodC": "{\"isActive\":true,\"trianglesCount\":18000,\"applyWeldingFix\":true,\"mergeExceptions\":\"$Lights\\r\\n$Mirrors\\r\\n*Wiper*\\r\\n*WING*\\r\\n*Rear_Cawl*\\r\\n*DOOR*\\r\\n*hood*\\r\\n*trunk*\\r\\n*blinker*\\r\\n*light*\\r\\n*brake*\\r\\n*ROOF*\\r\\n*reflector*\\r\\n*indicator*\\r\\n*reverse*\\r\\n*spoiler*\\r\\n*BONNET*\\r\\n*glass*\\r\\n*beam*\\r\\n*bulb*\\r\\n*turn*\\r\\n*lamp*\\r\\n*Blinker*\\r\\n*INNER*\\r\\nPlane004\\r\\nLEDs upper stop signal\\r\\n\",\"mergeParents\":\"$LightAnimation\\n$Wheels\\nREAR_AXLE\\nCOCKPIT_HR\",\"mergeAsBlack\":\"$BottomMeshes\",\"elementsToRemove\":\"$AnalogInstruments\\n$RimsBlurred\\n$Brakes\\n$BrokenGlass\\n$ExtraRotating\\n$InteriorExtras\\n$RemoveCompletely\\n$BrakesMeshes\\n$SuspensionMeshes\\n$InteriorMeshes & ( $ExtraRotating, $Lights, $LightAnimation, $Mirrors, $TransparentMeshes )\\nCOCKPIT_LR\\nCINTURE*\\n*Seatbelt*\\n\\n\",\"emptyNodesToKeep\":\"$ImportantNodes\",\"convertUv2\":\"\",\"elementsPriorities\":\"$InteriorMeshes = 0.4\\n$Lights = 1.8\\n$CarPaint = 3.2\\n$Tyres = 1.2\\n$WheelMeshes = 1.2\\n*light* = 3\\n*brake* = 3\\n*reflector* = 3\\n*indicator* = 3\\n*reverse* = 3\\n*beam* = 3\\n*bulb* = 3\\n*turn* = 3\\n*lamp* = 3\\n*Blinker* = 3\\n*wiper* = 3\\nPlane004 = 3\\nLEDs upper stop signal = 3\",\"offsetsAlongNormal\":\"$Stickers = 0.01\",\"keepTemporaryFiles\":false}", "LodD": "{\"isActive\":false,\"trianglesCount\":4000,\"applyWeldingFix\":true,\"mergeExceptions\":\"\",\"mergeParents\":\"\",\"mergeAsBlack\":\"$InteriorMeshes \",\"elementsToRemove\":\"Plane004\\nObject_11_SUB0\\nObject_12_SUB0\\nLED\\nPlane006\\nObject1120\\nLEDs upper stop signal\\nObject_12_SUB1\\nAlfa Romeo logo front\\nObject662\\nObject_11_SUB1\\n$AnalogInstruments\\n$Brakes\\n$BrakesMeshes\\n$BrokenGlass\\n$ExtraRotating\\n$InteriorExtras\\n$LicensePlate\\n$RemoveCompletely\\n$RimsBlurred\\n$SuspensionMeshes\\n$TransparentMeshes\\nCOCKPIT_LR\\nREAR_AXLE\\nactive:no\\nmaterialBlendMode:alphaTest\\n$InteriorMeshes & ( $ExtraRotating, $Lights, $LightAnimation, $Mirrors, aabbRelMaxY > 0.95 )\\n$BottomMeshes\\naabbSize<0.15\\n*light*\\n*brake*\\n*reflector*\\n*indicator*\\n*reverse*\\n*beam*\\n*bulb*\\n*turn*\\n*lamp*\\n*Blinker*\\n*glass*\\n*Plate*\\n*mirror*\\n*wiper*\\n*exhaust*\",\"emptyNodesToKeep\":\"$ImportantNodes\",\"elementsPriorities\":\"$InteriorMeshes = 0.1\\n$CarPaint = 5\\n$Tyres = 5\",\"offsetsAlongNormal\":\"$Stickers = 0.02\",\"keepTemporaryFiles\":false}"}}