[HEADER]
NAME=Race Hybrid

[CONTROLLER_0]
COMBINATOR=ADD 							; COMBINATOR MODE: ADD or MULT
INPUT=GAS								; telemetry channel input. SLIPRATIO_MAX SLIPRATIO_AVG LONG LATG BRAKE0-1 GAS0-1 STEER-1+1 SPEEDKMH GEAR
LUT=kers_gas1.lut						; input data to kers delivery lookup table
FILTER=0.96								; filter for eliminating spikes in variations and control variation speed
UP_LIMIT=1								; kers delivery max limit
DOWN_LIMIT=0							; kers delivery min limit (can be negative)

[CONTROLLER_1]
COMBINATOR=MULT 						
INPUT=GEAR						
LUT=kers_gear1.lut		
FILTER=0.96								
UP_LIMIT=1							
DOWN_LIMIT=0						

[CONTROLLER_2]
COMBINATOR=MULT 						
INPUT=SLIPRATIO_MAX						
LUT=kers_slipratio1.lut			
FILTER=0.96							
UP_LIMIT=1								
DOWN_LIMIT=-1		
