# Assetto Corsa LUT File Comparison Tool

This tool compares original vs tuned .lut files from the Alfa Romeo Junior 2024 Tuned car and generates visual comparison charts.

## Directory Structure
```
luts_comp/
├── compare_luts.py          # Main comparison script
├── original/                # Original .lut files (before tuning)
├── tuned/                   # Modified .lut files (after tuning)
├── comparison/              # Generated comparison charts (PNG files)
└── README.md               # This file
```

## Usage

### Prerequisites
- Python 3.6 or higher
- matplotlib library (`pip install matplotlib`)
- numpy library (`pip install numpy`)

### Running the Script
1. Open a terminal/command prompt
2. Navigate to the `luts_comp` directory
3. Run the script:
   ```bash
   python compare_luts.py
   ```

### Output
The script will:
- Automatically detect all matching .lut files in both `original/` and `tuned/` directories
- Generate comparison charts for each file pair
- Save charts as PNG images in the `comparison/` directory
- Display progress and summary information

## Key Files Compared

### Engine Performance
- **power.lut** - Engine power curve (RPM vs HP)
- **throttle.lut** - Throttle response mapping

### Aerodynamics
- **wing_body_AOA_CL.lut** - Body lift coefficient vs angle of attack
- **wing_body_AOA_CD.lut** - Body drag coefficient vs angle of attack
- **wing_front_AOA_CL.lut** - Front wing lift vs angle of attack
- **wing_rear_AOA_CL.lut** - Rear wing lift vs angle of attack
- **f_wing_controller_speed.lut** - Speed-dependent wing control (tuned only)

### Vehicle Dynamics
- **traction_control.lut** - Traction control intervention curve
- **understeer_factor_vs_lock.lut** - Differential lock vs understeer

## Chart Features
- **Blue line with circles**: Original configuration
- **Red line with squares**: Tuned configuration
- **Peak change percentage**: Shows improvement/change at peak values
- **Proper axis labels**: RPM, HP, coefficients, etc.
- **High-resolution output**: 300 DPI PNG files suitable for documentation

## Troubleshooting
- Ensure both `original/` and `tuned/` directories exist
- Check that .lut files have the correct format (x|y pairs)
- Install required Python packages if missing
- Files only present in one directory will be skipped with a warning

## Modifications Made
This comparison tool was created to visualize the changes made during the Alfa Romeo Junior 2024 Tuned performance optimization, including:
- Engine power increases (+19% peak power)
- Aerodynamic balance improvements
- High-speed stability enhancements
