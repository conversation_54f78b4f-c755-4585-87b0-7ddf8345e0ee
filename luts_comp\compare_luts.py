#!/usr/bin/env python3
"""
Assetto Corsa LUT File Comparison Tool
Compares original vs tuned .lut files and generates comparison charts

Author: Augment Agent
Date: 2025-07-07
"""

import os
import sys
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import re
from typing import Dict, List, Tuple, Optional

class LUTComparator:
    def __init__(self, base_dir: str = "."):
        """Initialize the LUT comparator with base directory."""
        self.base_dir = Path(base_dir)
        self.original_dir = self.base_dir / "original"
        self.tuned_dir = self.base_dir / "tuned"
        self.comparison_dir = self.base_dir / "comparison"
        
        # Create comparison directory if it doesn't exist
        self.comparison_dir.mkdir(exist_ok=True)
        
        # Define axis labels and units for different file types
        self.file_configs = {
            'power': {'x_label': 'RPM', 'y_label': 'Power (HP)', 'title': 'Engine Power Curve'},
            'wing_body_AOA_CL': {'x_label': 'Angle of Attack (°)', 'y_label': 'Lift Coefficient (CL)', 'title': 'Body Lift vs AOA'},
            'wing_body_AOA_CD': {'x_label': 'Angle of Attack (°)', 'y_label': 'Drag Coefficient (CD)', 'title': 'Body Drag vs AOA'},
            'wing_front_AOA_CL': {'x_label': 'Angle of Attack (°)', 'y_label': 'Lift Coefficient (CL)', 'title': 'Front Wing Lift vs AOA'},
            'wing_front_AOA_CD': {'x_label': 'Angle of Attack (°)', 'y_label': 'Drag Coefficient (CD)', 'title': 'Front Wing Drag vs AOA'},
            'wing_rear_AOA_CL': {'x_label': 'Angle of Attack (°)', 'y_label': 'Lift Coefficient (CL)', 'title': 'Rear Wing Lift vs AOA'},
            'wing_rear_AOA_CD': {'x_label': 'Angle of Attack (°)', 'y_label': 'Drag Coefficient (CD)', 'title': 'Rear Wing Drag vs AOA'},
            'f_wing_controller_speed': {'x_label': 'Speed (km/h)', 'y_label': 'Wing Angle (°)', 'title': 'Speed-Dependent Wing Control'},
            'throttle': {'x_label': 'Throttle Input (%)', 'y_label': 'Throttle Output (%)', 'title': 'Throttle Response Curve'},
            'traction_control': {'x_label': 'Slip Ratio', 'y_label': 'TC Intervention', 'title': 'Traction Control Curve'},
            'analog_speed_curve': {'x_label': 'Speed (km/h)', 'y_label': 'Gauge Reading', 'title': 'Speedometer Calibration'},
        }
    
    def parse_lut_file(self, file_path: Path) -> Tuple[List[float], List[float]]:
        """Parse a .lut file and return x, y data arrays."""
        x_data = []
        y_data = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Skip empty lines and comments
                    if not line or line.startswith(';') or line.startswith('#'):
                        continue
                    
                    # Parse data line (format: x|y)
                    if '|' in line:
                        try:
                            parts = line.split('|')
                            if len(parts) >= 2:
                                # Handle comma decimal separator (European format)
                                x_val = float(parts[0].replace(',', '.'))
                                y_val = float(parts[1].replace(',', '.'))
                                x_data.append(x_val)
                                y_data.append(y_val)
                        except ValueError as e:
                            print(f"Warning: Could not parse line {line_num} in {file_path.name}: '{line}' - {e}")
                            continue
                            
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return [], []
        
        return x_data, y_data
    
    def get_file_config(self, filename: str) -> Dict[str, str]:
        """Get configuration for a specific file type."""
        # Remove .lut extension for matching
        base_name = filename.replace('.lut', '')
        
        # Check for exact matches first
        if base_name in self.file_configs:
            return self.file_configs[base_name]
        
        # Check for partial matches
        for key, config in self.file_configs.items():
            if key in base_name:
                return config
        
        # Default configuration
        return {
            'x_label': 'X Value',
            'y_label': 'Y Value',
            'title': f'{base_name.replace("_", " ").title()} Comparison'
        }

    def create_comparison_chart(self, filename: str, original_data: Tuple[List[float], List[float]],
                              tuned_data: Tuple[List[float], List[float]]) -> bool:
        """Create a comparison chart for original vs tuned data."""
        try:
            # Get file configuration
            config = self.get_file_config(filename)

            # Unpack data
            orig_x, orig_y = original_data
            tuned_x, tuned_y = tuned_data

            # Skip if no data
            if not orig_x or not tuned_x:
                print(f"Warning: No data found for {filename}")
                return False

            # Create the plot
            plt.figure(figsize=(12, 8))

            # Plot original data
            plt.plot(orig_x, orig_y, 'b-', linewidth=2.5, label='Original', marker='o', markersize=4)

            # Plot tuned data
            plt.plot(tuned_x, tuned_y, 'r-', linewidth=2.5, label='Tuned', marker='s', markersize=4)

            # Customize the plot
            plt.xlabel(config['x_label'], fontsize=12, fontweight='bold')
            plt.ylabel(config['y_label'], fontsize=12, fontweight='bold')
            plt.title(config['title'], fontsize=14, fontweight='bold', pad=20)
            plt.legend(fontsize=11, loc='best')
            plt.grid(True, alpha=0.3)

            # Add difference annotation if applicable
            if len(orig_y) > 0 and len(tuned_y) > 0:
                # Calculate percentage change at peak values
                orig_peak = max(orig_y) if orig_y else 0
                tuned_peak = max(tuned_y) if tuned_y else 0

                if orig_peak != 0:
                    pct_change = ((tuned_peak - orig_peak) / orig_peak) * 100
                    change_text = f"Peak Change: {pct_change:+.1f}%"
                    plt.text(0.02, 0.98, change_text, transform=plt.gca().transAxes,
                            fontsize=10, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

            # Improve layout
            plt.tight_layout()

            # Save the chart
            output_filename = f"{filename.replace('.lut', '')}_comparison.png"
            output_path = self.comparison_dir / output_filename
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✓ Created comparison chart: {output_filename}")
            return True

        except Exception as e:
            print(f"Error creating chart for {filename}: {e}")
            return False

    def find_matching_files(self) -> List[str]:
        """Find .lut files that exist in both original and tuned directories."""
        if not self.original_dir.exists():
            print(f"Error: Original directory '{self.original_dir}' does not exist!")
            return []

        if not self.tuned_dir.exists():
            print(f"Error: Tuned directory '{self.tuned_dir}' does not exist!")
            return []

        # Get all .lut files from both directories
        original_files = set(f.name for f in self.original_dir.glob("*.lut"))
        tuned_files = set(f.name for f in self.tuned_dir.glob("*.lut"))

        # Find common files
        common_files = original_files.intersection(tuned_files)

        # Report files only in one directory
        only_original = original_files - tuned_files
        only_tuned = tuned_files - original_files

        if only_original:
            print(f"Files only in original: {', '.join(sorted(only_original))}")

        if only_tuned:
            print(f"Files only in tuned: {', '.join(sorted(only_tuned))}")

        return sorted(list(common_files))

    def compare_all_files(self) -> None:
        """Compare all matching .lut files and generate charts."""
        print("🔍 Assetto Corsa LUT File Comparison Tool")
        print("=" * 50)

        # Find matching files
        matching_files = self.find_matching_files()

        if not matching_files:
            print("❌ No matching .lut files found in both directories!")
            return

        print(f"📊 Found {len(matching_files)} matching .lut files to compare")
        print(f"📁 Output directory: {self.comparison_dir}")
        print()

        successful_comparisons = 0
        failed_comparisons = 0

        # Process each file
        for filename in matching_files:
            print(f"Processing: {filename}")

            # Parse original file
            original_path = self.original_dir / filename
            original_data = self.parse_lut_file(original_path)

            # Parse tuned file
            tuned_path = self.tuned_dir / filename
            tuned_data = self.parse_lut_file(tuned_path)

            # Create comparison chart
            if self.create_comparison_chart(filename, original_data, tuned_data):
                successful_comparisons += 1
            else:
                failed_comparisons += 1

        # Summary
        print()
        print("=" * 50)
        print(f"✅ Successfully created {successful_comparisons} comparison charts")
        if failed_comparisons > 0:
            print(f"❌ Failed to create {failed_comparisons} charts")
        print(f"📁 Charts saved in: {self.comparison_dir.absolute()}")

        # List key files that were compared
        key_files = ['power.lut', 'wing_rear_AOA_CL.lut', 'wing_body_AOA_CL.lut']
        compared_key_files = [f for f in key_files if f in matching_files]

        if compared_key_files:
            print()
            print("🎯 Key performance files compared:")
            for key_file in compared_key_files:
                chart_name = f"{key_file.replace('.lut', '')}_comparison.png"
                print(f"   • {key_file} → {chart_name}")


def main():
    """Main execution function."""
    try:
        # Initialize comparator
        comparator = LUTComparator()

        # Run comparison
        comparator.compare_all_files()

    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
